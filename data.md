
# 你精通dify.ai

你帮我把dify workflow yml dsl生成出来，你要像写代码一样严谨，dsl 不求每个细节都对，生成大致骨架，当然了能一把通过没错更好。

我想用dify 实现类似的agent fuction calling，保持扩展后续会添加更多function

当用户输入河南行政编码，那么结构化返回 function.py里面get_amap_district的入参，并且能够发起function calling调用
当用户输入北京天气，那么需要先查询出来北京行政编码，把北京行政编码作为 get_amap_weather 入参，获取天气

## function 实例用法
 
if __name__ == "__main__":
        # 测试行政区划查询
        print("\n--- 测试行政区划查询 ---")
        district_result = get_amap_district( keywords="北京")
        print(json.dumps(district_result, indent=4, ensure_ascii=False))

        district_result_error = get_amap_district( keywords="") # 测试错误情况
        print(json.dumps(district_result_error, indent=4, ensure_ascii=False))

        # 测试天气查询
        print("\n--- 测试天气查询 ---")
        # 首先获取北京的 adcode
        # 注意：在实际应用中，你可能需要更健壮的方式获取 adcode
        beijing_adcode = "北京" # 北京市的adcode
        weather_result_live = get_amap_weather( city_adcode=beijing_adcode, extensions="base")
        print("\n实况天气:")
        print(json.dumps(weather_result_live, indent=4, ensure_ascii=False))

        weather_result_forecast = get_amap_weather( city_adcode=beijing_adcode, extensions="all")
        print("\n预报天气:")
        print(json.dumps(weather_result_forecast, indent=4, ensure_ascii=False))

        weather_result_error = get_amap_weather( city_adcode="INVALID_ADCODE") # 测试错误情况
        print("\n错误天气查询:")
        print(json.dumps(weather_result_error, indent=4, ensure_ascii=False))

打印结果：

--- 测试行政区划查询 ---
{
    "result": {
        "success": true,   
        "message": "OK",   
        "count": "2",      
        "suggestion": {    
            "keywords": [],
            "cities": []
        },
        "districts": [
            {
                "citycode": "010",
                "adcode": "110000",
                "name": "北京市",
                "center": "116.407387,39.904179",
                "level": "province",
                "districts": []
            },
            {
                "citycode": "010",
                "adcode": "110100",
                "name": "北京城区",
                "center": "116.405285,39.904989",
                "level": "city",
                "districts": []
            }
        ],
        "infocode": "10000"
    }
}
{
    "result": {
        "success": true,
        "message": "OK",
        "count": "1",
        "suggestion": {
            "keywords": [],
            "cities": []
        },
        "districts": [
            {
                "citycode": [],
                "adcode": "100000",
                "name": "中华人民共和国",
                "center": "116.3683244,39.915085",
                "level": "country",
                "districts": []
            }
        ],
        "infocode": "10000"
    }
}

--- 测试天气查询 ---

实况天气:
{
    "result": {
        "success": true,
        "message": "OK",
        "count": "1",
        "infocode": "10000",
        "lives": [
            {
                "province": "北京",
                "city": "北京市",
                "adcode": "110000",
                "weather": "晴",
                "temperature": "28",
                "winddirection": "西",
                "windpower": "≤3",
                "humidity": "33",
                "reporttime": "2025-06-11 10:03:15",
                "temperature_float": "28.0",
                "humidity_float": "33.0"
            }
        ]
    }
}

预报天气:
{
    "result": {
        "success": true,
        "message": "OK",
        "count": "1",
        "infocode": "10000",
        "forecasts": [
            {
                "city": "北京市",
                "adcode": "110000",
                "province": "北京",
                "reporttime": "2025-06-11 10:03:15",
                "casts": [
                    {
                        "date": "2025-06-11",
                        "week": "3",
                        "dayweather": "晴",
                        "nightweather": "多云",
                        "daytemp": "35",
                        "nighttemp": "25",
                        "daywind": "南",
                        "nightwind": "南",
                        "daypower": "1-3",
                        "nightpower": "1-3",
                        "daytemp_float": "35.0",
                        "nighttemp_float": "25.0"
                    },
                    {
                        "date": "2025-06-12",
                        "week": "4",
                        "dayweather": "多云",
                        "nightweather": "阴",
                        "daytemp": "34",
                        "nighttemp": "22",
                        "daywind": "南",
                        "nightwind": "南",
                        "daypower": "1-3",
                        "nightpower": "1-3",
                        "daytemp_float": "34.0",
                        "nighttemp_float": "22.0"
                    },
                    {
                        "date": "2025-06-13",
                        "week": "5",
                        "dayweather": "阴",
                        "nightweather": "小雨",
                        "daytemp": "32",
                        "nighttemp": "22",
                        "daywind": "东南",
                        "nightwind": "东南",
                        "daypower": "1-3",
                        "nightpower": "1-3",
                        "daytemp_float": "32.0",
                        "nighttemp_float": "22.0"
                    },
                    {
                        "date": "2025-06-14",
                        "week": "6",
                        "dayweather": "雷阵雨",
                        "nightweather": "雷阵雨",
                        "daytemp": "33",
                        "nighttemp": "22",
                        "daywind": "西南",
                        "nightwind": "西南",
                        "daypower": "1-3",
                        "nightpower": "1-3",
                        "daytemp_float": "33.0",
                        "nighttemp_float": "22.0"
                    }
                ]
            }
        ]
    }
}

错误天气查询:
{
                        "daytemp_float": "33.0",
                        "nighttemp_float": "22.0"
                    }
                ]
            }
        ]
    }
}

错误天气查询:
{

错误天气查询:
{
    "result": {
        "success": true,
        "message": "OK",
        "count": "0",
        "infocode": "10000",
        "lives": []
    }
}