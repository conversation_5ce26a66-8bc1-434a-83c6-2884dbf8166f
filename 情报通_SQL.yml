app:
  description: ''
  icon: https://xai.anta.com/fastagi/baselineApi/api/fastagi-store/fastagi-cloud-files/20250606/5d729d6e-b544-4a80-a580-c1f32bb784e5/image-1749185981945.jpg
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 情报通_SQL
  use_icon_as_answer_icon: false
kind: app
version: 0.1.2
workflow:
  conversation_variables:
  - description: ''
    id: f2781a79-ae51-4516-b9ec-a4be55a609b6
    name: chat
    selector:
    - conversation
    - chat
    value: ''
    value_type: string
  environment_variables: []
  features:
    command:
      command_list: []
      enabled: false
    feedback:
      enabled: true
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 10
        file_size_limit: 50
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 🔍 我是情报通小助手，基于SQL查询各品牌在不同平台的指定时间范围数据！
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    show_chain:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 25年4月份，安踏在三平台的增速是多少
    - 25年4月京东平台上销售额排名前十的运动鞋服品牌有哪些？
    - 2025年母婴鞋服前5名的品牌是什么，增速、市场份额分别是多少？
    - 25年1月FILA天猫京东抖音三平台运动鞋服销额合计是多少
    - 特步2024年5月销量和安踏上个月的销售额
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: **********882-source-1742200118083-target
      selected: false
      source: '**********882'
      sourceHandle: source
      target: '1742200118083'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 1741749135606-source-1742194534736-target
      selected: false
      source: '1741749135606'
      sourceHandle: source
      target: '1742194534736'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: 1741748409330-source-1749439527180-target
      selected: false
      source: '1741748409330'
      sourceHandle: source
      target: '1749439527180'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1749439527180-source-1749440512624-target
      selected: false
      source: '1749439527180'
      sourceHandle: source
      target: '1749440512624'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1749440512624-source-1741749135606-target
      selected: false
      source: '1749440512624'
      sourceHandle: source
      target: '1741749135606'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1741748409330-source-1749445348714-target
      source: '1741748409330'
      sourceHandle: source
      target: '1749445348714'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 1749445348714-source-1749445378845-target
      source: '1749445348714'
      sourceHandle: source
      target: '1749445378845'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 1749445378845-source-1742200118083-target
      source: '1749445378845'
      sourceHandle: source
      target: '1742200118083'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: iteration
      id: 1742194534736-source-1749448387069-target
      source: '1742194534736'
      sourceHandle: source
      target: '1749448387069'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1749448387069'
        sourceType: iteration-start
        targetType: code
      id: 1749448387069start-source-1749448417359-target
      source: 1749448387069start
      sourceHandle: source
      target: '1749448417359'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1749448387069'
        sourceType: code
        targetType: http-request
      id: 1749448417359-source-1749448421210-target
      source: '1749448417359'
      sourceHandle: source
      target: '1749448421210'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: iteration
        targetType: llm
      id: 1749448387069-source-**********882-target
      source: '1749448387069'
      sourceHandle: source
      target: '**********882'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 52
      id: '1741748409330'
      position:
        x: 30
        y: 298.5
      positionAbsolute:
        x: 30
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            max_tokens: 3701
            response_format: json_object
            temperature: 0.1
          mode: chat
          name: gpt-4o
          provider: openai
        prompt_template:
        - id: 2286ecb2-7bbe-4a56-a378-d34330b3b348
          role: system
          text: '## 背景

            你是SQL专家,处理SQLite数据库中的电商数据分析。数据表名:情报通


            ## 数据表结构

            ### 字段说明

            维度字段:日期类型(TEXT)、行业(TEXT)、销售年月(TEXT)、平台(TEXT)、品牌名称(TEXT)

            指标字段:品牌销量 同期品牌销量 品牌销额 同期品牌销额 行业销量 同期行业销量 行业销额 同期行业销额 本期排名 同期排名 排名变化 本期行业份额
            同期行业份额 份额变化(均为REAL类型)


            ### 字段取值范围

            - 行业:母婴鞋服、运动鞋服、户外鞋服

            - 平台: 抖音、京东、天猫、全平台

            - 销售年月:YYYY-MM-01格式(如2025-02-01、2023-02-01)


            ## 核心要求

            1. 需求拆解:多需求时分别提供SQL查询

            2. 品牌名称转换:根据品牌名称映射表将用户输入的品牌名转换为标准中文名称

            3. 默认品牌:如用户询问中未明确指定品牌,默认为"安踏"品牌

            4. 默认平台:如用户询问中未明确指定平台,默认为"全平台"

            5. 默认行业:如用户询问中未明确指定行业,计算所有行业(母婴鞋服、运动鞋服、户外鞋服)的总和

            6. 增速计算:用户询问增速时,使用品牌销量、同期品牌销量进行计算,公式为:(品牌** - 同期品牌**) / 同期品牌** * 100


            **默认指标**:如用户询问中没有明确指定具体指标(如销量、销额),默认询问销售额,使用品牌销额计算


            7. 排名查询:本期排名字段表示某个具体月份、具体平台下该品牌的排名。询问排名相关问题时,需要明确指定销售年月和平台条件,然后使用本期排名字段进行筛选和排序

            8. 聚合计算:查询销量、销额等指标时,默认使用SUM()计算总和,除非用户明确要求明细数据

            9. 空值处理:同时考虑NULL和空字符串('''')情况

            10. 数值计算:除法运算使用* 1.0确保浮点数结果

            11. 字段命名:中文字段名用反引号,中文别名用引号

            12. 筛选显示:LIKE条件时返回被筛选字段名

            13. 时间处理:未指定时间时计算所有时期总和;处理年月筛选时使用LIKE模糊匹配(如2023年数据用销售年月 LIKE ''2023-%'')

            14. 结果优化:适当使用聚合、排序、限制行数

            15. SQL格式:生成单行SQL语句,不使用换行符,保持紧凑格式


            ## 输出格式

            ```json

            {"SQL":["单行SQL语句,不包含换行符",""……]}'
        - id: 61413907-a405-4aca-9051-bc1c89042496
          role: user
          text: '# 用户问题

            {{#1749440512624.text#}}'
        selected: false
        title: 生成SQL
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: '1741749135606'
      position:
        x: 936
        y: 298.5
      positionAbsolute:
        x: 936
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        code: "\ndef main(arg1: str) -> list:\n    import json\n    data_dict = json.loads(arg1)\n\
          \    return {\n        'result': data_dict['SQL']\n    }"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: 取SQL
        type: code
        variables:
        - value_selector:
          - '1741749135606'
          - text
          variable: arg1
      height: 52
      id: '1742194534736'
      position:
        x: 1238
        y: 298.5
      positionAbsolute:
        x: 1238
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: ''
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: true
            size: 10
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: qwen-max-latest
          provider: openai
        prompt_template:
        - id: 5319d037-e3a4-4ac1-a1ae-822b5c797490
          role: system
          text: '## 请结合用户问题重新表达，尽量以表格形式回答用户具体数据,无需透露SQL信息；


            ## 若数据结果包含数据，请注意：

            1. 若问百分比，请用**%回答；

            2. 保留小数点后3位，计算结果四舍五入

            3. 用表格的形式回答；

            4. 数额较大时，单位优化为万元；


            ## 若数据结果不包含数据，请注意：

            提示用户“抱歉，根据现有数据，暂未找到**数据”


            # 结果

            ## SQL

            {{#1742194534736.result#}}

            ##数据结果

            {{#1749448387069.output#}}

            '
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: '**********882'
      position:
        x: 2288
        y: 298.5
      positionAbsolute:
        x: 2288
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        answer: '{{#**********882.text#}}'
        desc: ''
        selected: false
        suggestion:
        - '1749445378845'
        - result
        title: 直接回复 2
        type: answer
        variables: []
      height: 104
      id: '1742200118083'
      position:
        x: 2590
        y: 298.5
      positionAbsolute:
        x: 2590
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        provider_id: time
        provider_name: time
        provider_type: builtin
        selected: false
        title: 获取当前时间
        tool_configurations:
          format: '%Y-%m-%d %H:%M:%S'
          timezone: UTC
        tool_label: 获取当前时间
        tool_name: current_time
        tool_parameters: {}
        type: tool
      height: 114
      id: '1749439527180'
      position:
        x: 332
        y: 298.5
      positionAbsolute:
        x: 332
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: true
            size: 10
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: qwen-max
          provider: openai
        prompt_template:
        - id: 73a78341-56fe-4361-a364-c5567a2635c9
          role: system
          text: "## 角色定义\n你是一个专业的数据查询问题改写助手，负责将用户的自然语言查询改写为标准化、明确的查询表达。\n## 当前时间信息\n\
            {{#1749439527180.text#}}\n## 数据表结构\n表名：情报通\n### 字段信息：\n日期类型：数据类型标识\n行业：如\"\
            母婴鞋服\"\n销售年月：格式为\"YYYY/M/D\"，如\"2023/1/1\"\n平台：如\"全平台\"\n品牌名称：如\"巴拉巴拉\"\
            \n品牌销量：品牌销售数量\n同期品牌销量：去年同期的品牌销售数量\n品牌销额：品牌销售金额\n同期品牌销额：去年同期的品牌销售金额\n行业销量：整个行业销售数量\n\
            同期行业销量：去年同期行业销售数量\n行业销额：整个行业销售金额\n同期行业销额：去年同期行业销售金额\n本期排名：当前期间排名\n同期排名：去年同期排名\n\
            排名变化：本期排名-同期排名\n本期行业份额：当前市场份额百分比\n同期行业份额：去年同期市场份额百分比\n份额变化：本期份额-同期份额\n\
            ### 改写规则：\n1. 时间表达标准化\n\"上个月\" → \"2025年5月\"\n\"去年\" → \"2024年\"\n\"今年\"\
            \ → \"2025年\"\n\"上年同期\" → \"2024年6月\"\n\"去年同期\" → \"2024年6月\"\n\"最近三个月\"\
            \ → \"2025年3月到2025年5月\"\n\"本季度\" → \"2025年第二季度\"\n\"上季度\" → \"2025年第一季度\"\
            \n\"年初至今\" → \"2025年1月到2025年6月\"\n2. 业务实体标准化\n行业标准值：\n母婴鞋服\n户外鞋服\n运动鞋服\n\
            行业映射：\n\"母婴\" → \"母婴鞋服\"\n\"童装\" → \"母婴鞋服\"\n\"婴幼儿用品\" → \"母婴鞋服\"\n\"\
            户外\" → \"户外鞋服\"\n\"运动\" → \"运动鞋服\"\n\"体育用品\" → \"运动鞋服\"\n平台标准值：\n京东\n\
            抖音\n天猫\n全平台\n平台映射：\n\"所有平台\" → \"全平台\"\n\"全部平台\" → \"全平台\"\n\"整体平台\" →\
            \ \"全平台\"\n\"JD\" → \"京东\"\n\"淘宝\" → \"天猫\"\n\"Tmall\" → \"天猫\"\n若问题中没有提及平台，则默认映射全平台；\n\
            3. 指标维度标准化\n销量相关：\n\"销量/卖了多少/销售数量\" → \"品牌销量\"\n\"行业销量/整体销量\" → \"行业销量\"\
            \n销额相关：\n\"销售额/营业额/收入/金额\" → \"品牌销额\"\n\"行业销售额/整体销售额\" → \"行业销额\"\n排名相关：\n\
            \"排名/排第几/名次/排行\" → \"本期排名\"\n\"去年排名/同期排名\" → \"同期排名\"\n份额相关：\n\"市场份额/占比/份额\"\
            \ → \"本期行业份额\"\n\"去年份额/同期份额\" → \"同期行业份额\"\n变化趋势：\n\"排名变化/排名升降\" → \"\
            排名变化\"\n\"份额变化/份额增减\" → \"份额变化\"\n4. 比较分析标准化\n\"同比增长\" → \"品牌销量与同期品牌销量的对比\"\
            \n\"排名上升\" → \"排名变化 > 0\"\n\"排名下降\" → \"排名变化 < 0\"\n\"份额增长\" → \"份额变化\
            \ > 0\"\n\"超越去年\" → \"本期指标 > 同期指标\n5.品牌名称映射\n用户询问时的品牌名称需要转换为数据库中的标准名称:\n\
            - FILA/flia → 斐乐\n- Nike → 耐克\n- Adidas → 阿迪\n- lining → 李宁\n\n### 默认值规则\n\
            默认品牌： 未指定品牌时默认补充为\"安踏\"\n默认平台： 未指定平台时默认补充为\"全平台\"\n默认行业： 未指定行业时计算所有行业（母婴鞋服、运动鞋服、户外鞋服）的总和\n\
            默认指标： 未明确指标时默认查询销售额（品牌销额）\n\n### \n## 输出要求\n直接输出改写后的问题，使用标准化的表达方式。不需要JSON格式，只需要一句清晰、明确的改写后问题。\n\
            ### 示例\n用户查询： \"上个月巴拉在母婴行业的销量排名怎么样？\"\n改写输出： \"2025年5月巴拉巴拉在母婴鞋服行业全平台的销量本期排名是多少？\"\
            \n用户查询： \"去年同期巴拉巴拉的销售额是多少？\"\n改写输出： \"2024年6月巴拉巴拉的品牌销额是多少？\"\n用户查询： \"\
            今年母婴行业整体销量增长情况\"\n改写输出： \"2025年母婴鞋服行业销量与2024年母婴鞋服行业销量的对比情况\""
        selected: false
        title: 问题改写
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: '1749440512624'
      position:
        x: 634
        y: 298.5
      positionAbsolute:
        x: 634
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            response_format: json_object
            temperature: 0.7
          mode: chat
          name: deepseek-v3
          provider: openai
        prompt_template:
        - id: 42a30e1d-98ea-4a26-8bf3-d981d92ad9c5
          role: system
          text: '## 基于数据库请给出用户可能还想问的3个问题，json格式输出

            ## 数据表结构

            ### 字段说明

            维度字段:日期类型(TEXT)、行业(TEXT)、销售年月(TEXT)、平台(TEXT)、品牌名称(TEXT)

            指标字段:品牌销量 同期品牌销量 品牌销额 同期品牌销额 行业销量 同期行业销量 行业销额 同期行业销额 本期排名 同期排名 排名变化 本期行业份额
            同期行业份额 份额变化(均为REAL类型)


            ### 字段取值范围

            - 行业:母婴鞋服、运动鞋服、户外鞋服

            - 平台: 抖音、京东、天猫、全平台

            - 销售年月:YYYY-MM-01格式(如2025-02-01、2023-02-01)

            ## 输出格式

            {\n  \"relatedQuestions\": ["相似问题一","相似问题二"… ]'
        - id: b6a5afc4-0e0d-4b9d-9852-f2a618e28164
          role: user
          text: '{{#sys.query#}}'
        selected: false
        title: 大模型 4
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: '1749445348714'
      position:
        x: 1763
        y: 535.5
      positionAbsolute:
        x: 1763
        y: 535.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        code: "def main(arg1: str) -> dict:\n    import json\n    strJson = json.loads(arg1)\n\
          \    result = strJson.get('relatedQuestions',[])\n    return {\"result\"\
          : result}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: 代码执行 3
        type: code
        variables:
        - value_selector:
          - '1749445348714'
          - text
          variable: arg1
      height: 52
      id: '1749445378845'
      position:
        x: 2288
        y: 507.5
      positionAbsolute:
        x: 2288
        y: 507.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        height: 197
        iterator_selector:
        - '1742194534736'
        - result
        output_selector:
        - '1749448421210'
        - body
        output_type: array[string]
        selected: false
        start_node_id: 1749448387069start
        title: 循环
        type: iteration
        width: 688
      height: 197
      id: '1749448387069'
      position:
        x: 1540
        y: 298.5
      positionAbsolute:
        x: 1540
        y: 298.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 688
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1749448387069start
      parentId: '1749448387069'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 1564
        y: 366.5
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        code: "import base64\n\ndef main(arg1: str) -> dict:\n    encoded_string =\
          \ base64.b64encode(arg1.encode())\n    return {'result': encoded_string.decode()}"
        code_language: python3
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1749448387069'
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码执行 4
        type: code
        variables:
        - value_selector:
          - '1749448387069'
          - item
          variable: arg1
      height: 52
      id: '1749448417359'
      parentId: '1749448387069'
      position:
        x: 128
        y: 68
      positionAbsolute:
        x: 1668
        y: 366.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - id: key-value-2985
            key: ''
            type: text
            value: '{"result_key": "{{#1749448417359.result#}}"}'
          type: json
        desc: ''
        headers: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1749448387069'
        method: post
        params: ''
        retry_config:
          max_retries: 3
          retry_enabled: false
          retry_interval: 100
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: API 调用 2
        type: http-request
        url: http://10.232.153.230:8088/filter_data_md
        variables: []
      height: 108
      id: '1749448421210'
      parentId: '1749448387069'
      position:
        x: 430
        y: 68
      positionAbsolute:
        x: 1970
        y: 366.5
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    viewport:
      x: -593.1595974633472
      y: -162.24057771080055
      zoom: 1.0316971206024876
